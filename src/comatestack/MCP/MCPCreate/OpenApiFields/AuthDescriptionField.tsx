import {Path} from '@panda-design/path-form';
import {Form, Input} from 'antd';
import {useWatch} from 'antd/es/form/Form';
import {useEffect, useRef} from 'react';
import {setSystemUpdating} from '../../MCPEdit/hooks';

interface Props {
    path?: Path;
}

const AuthDescription = ({path = []}: Props) => {
    const form = Form.useFormInstance();
    const authType = useWatch([...path, 'serverConf', 'serverExtension', 'serverAuthType'], form);
    const authDescription = useWatch([...path, 'serverConf', 'serverExtension', 'authDescription'], form);
    const userModifiedContent = useWatch(
        [...path, 'serverConf', 'serverExtension', '_userModifiedContent'],
        form
    );
    const userHasModified = useWatch(
        [...path, 'serverConf', 'serverExtension', '_userHasModified'],
        form
    );

    const isInitializedRef = useRef(false);

    const defaultCloudIamContent = '根据AccessKey SecretKey进行认证，获取方式参考 云上百度（百度云度厂版）相关文档 https://cloud.baidu-int.com/icloud/help/%E4%BC%81%E4%B8%9A%E7%BB%84%E7%BB%87/%E6%9C%8D%E5%8A%A1%E7%94%A8%E6%88%B7/%E5%88%9B%E5%BB%BA%E6%9C%8D%E5%8A%A1%E7%94%A8%E6%88%B7/';

    useEffect(
        () => {
            const userModifiedFieldPath = [...path, 'serverConf', 'serverExtension', '_userModifiedContent'];
            const userHasModifiedFieldPath = [...path, 'serverConf', 'serverExtension', '_userHasModified'];

            if (!isInitializedRef.current && authDescription !== undefined) {
                if (authDescription !== '' && authDescription !== null && authDescription !== defaultCloudIamContent) {
                    setSystemUpdating(true);
                    try {
                        form.setFieldValue(userModifiedFieldPath, authDescription);
                        form.setFieldValue(userHasModifiedFieldPath, true);
                    } finally {
                        setSystemUpdating(false);
                    }
                }
                isInitializedRef.current = true;
            }
        },
        [authDescription, form, path, defaultCloudIamContent]
    );

    useEffect(
        () => {
            const fieldPath = [...path, 'serverConf', 'serverExtension', 'authDescription'];

            if (!isInitializedRef.current) {
                return;
            }

            if (authType === 'NONE') {
                return;
            }

            const hasUserModified = userHasModified === true;
            const hasNonEmptyUserContent = userModifiedContent && userModifiedContent.trim() !== '';

            let newValue = '';

            if (authType === 'CLOUD_INIT_IAM') {
                if (hasUserModified && hasNonEmptyUserContent) {
                    newValue = userModifiedContent;
                } else {
                    newValue = defaultCloudIamContent;
                }
            } else if (authType === 'OTHER') {
                if (hasUserModified) {
                    newValue = userModifiedContent || '';
                } else {
                    newValue = '';
                }
            }

            setSystemUpdating(true);
            try {
                form.setFieldValue(fieldPath, newValue);
            } finally {
                setSystemUpdating(false);
            }
        },
        [authType, userModifiedContent, userHasModified, form, path, defaultCloudIamContent]
    );

    const handleChange = (e: any) => {
        const value = e.target.value;
        const userModifiedFieldPath = [...path, 'serverConf', 'serverExtension', '_userModifiedContent'];
        const userHasModifiedFieldPath = [...path, 'serverConf', 'serverExtension', '_userHasModified'];

        setSystemUpdating(true);
        try {
            form.setFieldValue(userModifiedFieldPath, value);
            form.setFieldValue(userHasModifiedFieldPath, true);
        } finally {
            setSystemUpdating(false);
        }
    };

    if (authType === 'NONE') {
        return null;
    }

    const isRequired = authType === 'CLOUD_INIT_IAM' || authType === 'OTHER';
    const placeholder = authType === 'OTHER'
        ? '请说明鉴权的方法以及获取鉴权凭证的方式'
        : '请输入鉴权方法';

    return (
        <>
            <Form.Item
                name={[...path, 'serverConf', 'serverExtension', '_userModifiedContent']}
                style={{display: 'none'}}
            >
                <Input />
            </Form.Item>
            <Form.Item
                name={[...path, 'serverConf', 'serverExtension', '_userHasModified']}
                style={{display: 'none'}}
            >
                <Input />
            </Form.Item>
            <Form.Item
                label="鉴权方法"
                name={[...path, 'serverConf', 'serverExtension', 'authDescription']}
                rules={isRequired ? [{required: true, message: '请输入鉴权方法'}] : []}
                validateTrigger={['onBlur', 'onChange']}
            >
                <Input.TextArea
                    placeholder={placeholder}
                    autoSize={{minRows: 3}}
                    onChange={handleChange}
                />
            </Form.Item>
        </>
    );
};

export default AuthDescription;

